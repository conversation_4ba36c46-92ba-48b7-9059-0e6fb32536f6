#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接测试AI导演功能（无需启动Evennia服务器）
"""

import os
import sys
import time

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟必要的环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')

print("=" * 60)
print("AI导演系统直接测试")
print("=" * 60)

# 创建模拟的AI客户端
class MockOpenAI:
    class Completions:
        def create(self, **kwargs):
            class Choice:
                class Message:
                    content = '''{"title": "逆天改命", "theme": "凡人逆天修仙", "main_conflict": "废灵根与天道对抗", "key_characters": ["林逸风", "苏清雪"], "major_plot_points": [{"phase": "序章", "description": "觉醒特殊体质"}], "expected_phases": ["序章", "起承", "高潮", "转合", "终章"]}'''
            
            class Response:
                choices = [Choice()]
            
            return Response()
    
    class ChatCompletions:
        def create(self, **kwargs):
            class Choice:
                class Message:
                    content = '''{"title": "逆天改命", "theme": "凡人逆天修仙", "main_conflict": "废灵根与天道对抗", "key_characters": ["林逸风", "苏清雪"], "major_plot_points": [{"phase": "序章", "description": "觉醒特殊体质"}], "expected_phases": ["序章", "起承", "高潮", "转合", "终章"]}'''
            
            class Response:
                choices = [Choice()]
            
            return Response()
    
    def __init__(self, **kwargs):
        self.chat = self.ChatCompletions()
        self.completions = self.Completions()

# 替换openai模块
sys.modules['openai'] = type('openai', (), {'OpenAI': MockOpenAI})

# 现在导入AI导演相关模块
try:
    from systems.ai_director import AIDirector, StoryPhase, DecisionType
    print("✓ 成功导入AI导演模块")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)

def test_story_outline_parsing():
    """测试故事大纲解析"""
    print("\n1. 测试故事大纲解析")
    print("-" * 40)
    
    ai_director = AIDirector()
    
    story_outline = """
    《逆天改命》
    
    主题：凡人逆天修仙，挑战命运束缚
    
    核心冲突：主角作为废灵根的凡人，在修仙界备受歧视，但通过特殊机缘获得了改变命运的机会，
    与各大宗门、天道意志产生冲突。
    
    主要角色：
    - 林逸风：主角，废灵根却有惊人悟性
    - 苏清雪：青云宗天才弟子
    
    剧情要点：
    1. 序章：林逸风觉醒特殊体质
    2. 起承：拜入青云宗外门
    3. 高潮：各方势力争夺秘密
    """
    
    try:
        print("开始解析故事大纲...")
        outline = ai_director.analyze_story_outline(story_outline)
        
        print(f"✅ 解析成功!")
        print(f"   标题: {outline.title}")
        print(f"   主题: {outline.theme}")
        print(f"   核心冲突: {outline.main_conflict}")
        print(f"   关键角色: {', '.join(outline.key_characters)}")
        print(f"   剧情点数: {len(outline.major_plot_points)}")
        print(f"   故事阶段: {[phase.value for phase in outline.expected_phases]}")
        
        return True
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_decision():
    """测试AI决策"""
    print("\n2. 测试AI决策生成")
    print("-" * 40)
    
    ai_director = AIDirector()
    
    event_data = {
        "event_type": "CultivationBreakthroughEvent",
        "description": "林逸风成功突破到筑基期",
        "character": "林逸风",
        "location": "青云峰",
        "priority": "HIGH"
    }
    
    try:
        print("生成AI决策...")
        decision = ai_director.make_decision(event_data)
        
        print(f"✅ 决策生成成功!")
        print(f"   决策类型: {decision.decision_type.value}")
        print(f"   决策内容: {decision.content[:100]}...")
        print(f"   置信度: {decision.confidence:.2f}")
        print(f"   响应时间: {decision.response_time*1000:.1f}ms")
        
        return True
    except Exception as e:
        print(f"❌ 决策生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """测试性能"""
    print("\n3. 测试性能要求")
    print("-" * 40)
    
    ai_director = AIDirector()
    
    # 预热
    ai_director.make_decision({"event_type": "test", "description": "test"})
    
    response_times = []
    
    print("执行10次决策测试...")
    for i in range(10):
        event_data = {
            "event_type": f"TestEvent_{i}",
            "description": f"测试事件 {i}",
            "character": "测试角色",
            "timestamp": time.time()
        }
        
        decision = ai_director.make_decision(event_data)
        response_times.append(decision.response_time)
        print(f"  决策 {i+1}: {decision.response_time*1000:.1f}ms")
    
    avg_time = sum(response_times) / len(response_times)
    print(f"\n平均响应时间: {avg_time*1000:.1f}ms")
    
    if avg_time < 0.2:
        print("✅ 性能测试通过 (<200ms)")
        return True
    else:
        print("❌ 性能测试失败 (>200ms)")
        return False

def main():
    """运行所有测试"""
    results = []
    
    # 测试1: 故事大纲解析
    results.append(("故事大纲解析", test_story_outline_parsing()))
    
    # 测试2: AI决策生成
    results.append(("AI决策生成", test_ai_decision()))
    
    # 测试3: 性能测试
    results.append(("性能测试", test_performance()))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过!")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败")

if __name__ == "__main__":
    main()