# 项目关键凭证

本文档记录了项目开发环境中的所有重要登录信息，请妥善保管。

---

## 1. WSL (Windows Subsystem for Linux)

-   **用途**: 主要的开发和运行环境，模拟生产环境（Ubuntu）。
-   **用户名**: `karlo`
-   **密码**: `zy123good`

---

## 2. PostgreSQL 数据库

-   **用途**: 游戏核心数据存储。
-   **用户名**: `postgres`
-   **密码**: `zy123good`
-   **数据库名**: `xiuxian_mud`
-   **端口**: `5432`

---

## 3. Evennia 游戏超级管理员

-   **用途**: 游戏内的最高权限账户，用于调试、管理和测试。
-   **用户名**: `admin`
-   **密码**: `AdminTest123!`

---

## 4. Redis 缓存

-   **用途**: 缓存和会话管理，提升性能。
-   **地址**: `redis://127.0.0.1:6379/1`
-   **密码**: (未设置)

--- 