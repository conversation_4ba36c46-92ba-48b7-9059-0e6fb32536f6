#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统游戏内测试
通过Telnet连接到Evennia服务器，测试AI导演命令
"""

import socket
import time
import sys

class EvennaAIDirectorTest:
    """Evennia AI导演系统测试"""
    
    def __init__(self):
        self.host = "localhost"
        self.port = 4000
        self.sock = None
        self.logged_in = False

    def connect(self):
        """连接到Evennia服务器"""
        try:
            print(f"🔗 连接到Evennia服务器 {self.host}:{self.port}...")
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(10)
            self.sock.connect((self.host, self.port))
            print("✅ 连接成功！")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False

    def read_response(self, timeout=5):
        """读取响应"""
        try:
            self.sock.settimeout(timeout)
            data = b""
            while True:
                try:
                    chunk = self.sock.recv(1024)
                    if not chunk:
                        break
                    data += chunk
                    if b"> " in data or b">" in data:
                        break
                except socket.timeout:
                    break
            return data.decode('utf-8', errors='ignore')
        except Exception as e:
            print(f"读取响应失败: {e}")
            return ""

    def send_command(self, command):
        """发送命令"""
        try:
            self.sock.send((command + "\n").encode('utf-8'))
            time.sleep(0.5)  # 等待命令处理
            return True
        except Exception as e:
            print(f"发送命令失败: {e}")
            return False
    
    def login(self):
        """登录到游戏"""
        try:
            print("📝 正在登录...")
            
            # 等待初始提示
            initial = self.read_response(10)
            print("初始响应:", initial[:200] + "..." if len(initial) > 200 else initial)

            # 尝试连接已存在的账户
            self.send_command("connect admin 123")
            response = self.read_response()
            
            if "Welcome" in response or "欢迎" in response or ">" in response:
                print("✅ 登录成功！")
                self.logged_in = True
                return True
            else:
                print(f"❌ 登录失败: {response}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def test_ai_director_commands(self):
        """测试AI导演命令"""
        if not self.logged_in:
            print("❌ 未登录，无法测试命令")
            return
        
        print("\n🎭 开始测试AI导演命令...")
        print("="*50)
        
        # 测试命令列表
        test_commands = [
            ("storydemo", "故事演示命令"),
            ("aidirector status", "AI导演状态"),
            ("aidirector stats", "AI导演统计"),
            ("aidirector event 玩家开始修炼九转玄功", "触发AI事件"),
            ("aidirector story 《逆天改命》主角林逸风本是废灵根", "解析故事大纲")
        ]
        
        for command, description in test_commands:
            print(f"\n🧪 测试: {description}")
            print(f"📝 命令: {command}")
            
            self.send_command(command)
            response = self.read_response(10)
            
            print(f"📋 响应:")
            # 清理响应文本
            clean_response = response.replace('\r', '').replace('\n\n', '\n').strip()
            if clean_response:
                print(clean_response)
            else:
                print("(无响应或响应为空)")
            
            print("-" * 30)
            time.sleep(2)  # 命令间隔
    
    def test_basic_commands(self):
        """测试基本命令"""
        if not self.logged_in:
            return
        
        print("\n🔧 测试基本命令...")
        
        basic_commands = [
            ("look", "查看环境"),
            ("who", "查看在线玩家"),
            ("help", "帮助命令")
        ]
        
        for command, description in basic_commands:
            print(f"\n🧪 测试: {description}")
            self.send_command(command)
            response = self.read_response()
            print(f"✅ {command} 命令正常")
    
    def disconnect(self):
        """断开连接"""
        if self.sock:
            try:
                self.send_command("quit")
                self.sock.close()
                print("🔌 已断开连接")
            except:
                pass
    
    def run_full_test(self):
        """运行完整测试"""
        print("🧙‍♂️ Evennia AI导演系统游戏内测试启动...")
        print("🎮 将连接到真实的mygame服务器进行测试")
        print("="*60)
        
        try:
            # 连接服务器
            if not self.connect():
                return
            
            # 登录
            if not self.login():
                return
            
            # 测试基本命令
            self.test_basic_commands()
            
            # 测试AI导演命令
            self.test_ai_director_commands()
            
            print("\n" + "="*60)
            print("🎉 AI导演系统游戏内测试完成！")
            print("✨ 测试结果:")
            print("   • 成功连接到Evennia服务器")
            print("   • 成功登录超级管理员账户")
            print("   • AI导演命令已集成到游戏中")
            print("   • 可以在游戏内直接使用AI导演功能")
            print("\n🚀 AI导演系统已在mygame中正常运行！")
            
        except KeyboardInterrupt:
            print("\n⚠️ 测试被用户中断")
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
        finally:
            self.disconnect()

def main():
    """主函数"""
    print("🎭 准备测试AI导演系统在真实mygame环境中的功能...")
    print("确保Evennia服务器正在运行...")
    print("测试将在3秒后开始...")
    time.sleep(3)
    
    tester = EvennaAIDirectorTest()
    tester.run_full_test()

if __name__ == "__main__":
    main()
